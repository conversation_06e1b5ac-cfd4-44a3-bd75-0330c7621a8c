{"name": "@univerjs-infra/shared", "version": "0.10.0", "private": true, "description": "Some infrastructures for univerjs", "author": "DreamNum <<EMAIL>>", "license": "Apache-2.0", "homepage": "https://github.com/dream-num/univer", "repository": {"type": "git", "url": "https://github.com/dream-num/univer.git"}, "keywords": [], "exports": {"./eslint": "./eslint/index.ts", "./tsconfigs/*": "./tsconfigs/*.json", "./vite": "./vite/index.ts", "./vitest": "./vitest/index.js", "./esbuild": "./esbuild/index.ts", "./tailwind": "./tailwind/tailwind.config.ts", "./postcss": "./postcss/postcss.config.mjs"}, "main": "bin/index.js", "bin": {"univer-cli": "bin/index.ts"}, "dependencies": {"@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.7.0", "@vitejs/plugin-vue": "^6.0.1", "@vitest/coverage-istanbul": "^3.2.4", "autoprefixer": "^10.4.21", "eslint-plugin-better-tailwindcss": "^3.7.2", "fs-extra": "^11.3.0", "happy-dom": "18.0.1", "javascript-obfuscator": "^4.1.1", "postcss-preset-env": "^10.2.4", "postcss-replace": "^2.0.1", "sort-keys": "^5.1.0", "tailwind-scrollbar": "^4.0.2", "tailwindcss": "3.4.17", "vite": "^7.0.6", "vite-plugin-dts": "^4.5.4", "vite-plugin-external": "^6.2.2", "vitest": "^3.2.4"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@univerjs/icons": "^0.4.6", "@univerjs/icons-svg": "^0.4.6", "@univerjs/protocol": "0.1.46"}}