/**
 * Copyright 2023-present DreamNum Co., Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { UniverRenderingContext2D } from '@univerjs/engine-render';
import { Rect } from '@univerjs/engine-render';

const BUTTON_VIEWPORT = 16;

// Triangle dropdown icon path - pointing down
export const FILTER_BUTTON_TRIANGLE = new Path2D('M4 6L8 10L12 6Z');

export class FilterButton {
    static drawNoCriteria(ctx: UniverRenderingContext2D, size: number, fgColor: string, bgColor: string): void {
        ctx.save();

        Rect.drawWith(ctx, {
            radius: 2,
            width: BUTTON_VIEWPORT,
            height: BUTTON_VIEWPORT,
            fill: bgColor,
        });

        ctx.scale(size / BUTTON_VIEWPORT, size / BUTTON_VIEWPORT);
        ctx.fillStyle = fgColor;
        ctx.fill(FILTER_BUTTON_TRIANGLE);
        ctx.restore();
    }

    static drawHasCriteria(ctx: UniverRenderingContext2D, size: number, fgColor: string, bgColor: string): void {
        ctx.save();

        Rect.drawWith(ctx, {
            radius: 2,
            width: BUTTON_VIEWPORT,
            height: BUTTON_VIEWPORT,
            fill: bgColor,
        });

        ctx.scale(size / BUTTON_VIEWPORT, size / BUTTON_VIEWPORT);
        ctx.fillStyle = fgColor;
        ctx.fill(FILTER_BUTTON_TRIANGLE);
        ctx.restore();
    }
}
