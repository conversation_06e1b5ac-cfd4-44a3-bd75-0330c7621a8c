/**
 * Copyright 2023-present DreamNum Co., Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type enUS from './en-US';

const locale: typeof enUS = {
    BETADIST: {
        description: 'Возвращает интегральную функцию плотности бета-вероятности',
        abstract: 'Возвращает интегральную функцию плотности бета-вероятности',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B1%D0%B5%D1%82%D0%B0%D1%80%D0%B0%D1%81%D0%BF-49f1b9a9-a5da-470f-8077-5f1730b5fd47',
            },
        ],
        functionParameter: {
            x: { name: 'x', detail: 'Значение в интервале между A и B, для которого вычисляется функция.' },
            alpha: { name: 'альфа', detail: 'Параметр распределения.' },
            beta: { name: 'бета', detail: 'Параметр распределения.' },
            A: { name: 'A', detail: 'Нижняя граница интервала изменения x.' },
            B: { name: 'B', detail: 'Верхняя граница интервала изменения x.' },
        },
    },
    BETAINV: {
        description: 'Возвращает обратную интегральную функцию плотности бета-вeроятности указанного бета-распределения',
        abstract: 'Возвращает обратную интегральную функцию плотности бета-вeроятности указанного бета-распределения',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B1%D0%B5%D1%82%D0%B0%D0%BE%D0%B1%D1%80-8b914ade-b902-43c1-ac9c-c05c54f10d6c',
            },
        ],
        functionParameter: {
            probability: { name: 'вероятность', detail: 'Вероятность, связанная с бета-распределением.' },
            alpha: { name: 'альфа', detail: 'Параметр распределения.' },
            beta: { name: 'бета', detail: 'Параметр распределения.' },
            A: { name: 'A', detail: 'Нижняя граница интервала изменения x.' },
            B: { name: 'B', detail: 'Верхняя граница интервала изменения x.' },
        },
    },
    BINOMDIST: {
        description: 'Возвращает отдельное значение биномиального распределения',
        abstract: 'Возвращает отдельное значение биномиального распределения',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B1%D0%B8%D0%BD%D0%BE%D0%BC%D1%80%D0%B0%D1%81%D0%BF-506a663e-c4ca-428d-b9a8-05583d68789c',
            },
        ],
        functionParameter: {
            numberS: { name: 'число успехов', detail: 'Количество успешных испытаний.' },
            trials: { name: 'число испытаний', detail: 'Количество независимых испытаний.' },
            probabilityS: { name: 'вероятность успеха ', detail: 'Вероятность успеха каждого испытания. ' },
            cumulative: { name: 'накопительная', detail: 'Логическое значение, определяющее форму функции. Если кумулятив имеет значение TRUE, функция BINOMDIST возвращает функцию накопительного распределения, которая является вероятностью наличия не более числа успехов. Если значение FALSE, возвращается функция вероятностной массы, то есть вероятность числа успехов.' },
        },
    },
    CHIDIST: {
        description: 'Возвращает правостороннюю вероятность распределения хи-квадрат',
        abstract: 'Возвращает правостороннюю вероятность распределения хи-квадрат',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D1%85%D0%B82%D1%80%D0%B0%D1%81%D0%BF-c90d0fbc-5b56-4f5f-ab57-34af1bf6897e',
            },
        ],
        functionParameter: {
            x: { name: 'x', detail: 'Значение, для которого требуется вычислить распределение.' },
            degFreedom: { name: 'степени свободы', detail: 'Число степеней свободы.' },
        },
    },
    CHIINV: {
        description: 'Возвращает значение, обратное правосторонней вероятности распределения хи-квадрат',
        abstract: 'Возвращает значение, обратное правосторонней вероятности распределения хи-квадрат',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D1%85%D0%B82%D0%BE%D0%B1%D1%80-cfbea3f6-6e4f-40c9-a87f-20472e0512af',
            },
        ],
        functionParameter: {
            probability: { name: 'вероятность', detail: ' Вероятность, связанная с распределением хи-квадрат.' },
            degFreedom: { name: 'степени свободы', detail: 'Число степеней свободы.' },
        },
    },
    CHITEST: {
        description: 'Возвращает критерий независимости',
        abstract: 'Возвращает критерий независимости',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D1%85%D0%B82%D1%82%D0%B5%D1%81%D1%82-981ff871-b694-4134-848e-38ec704577ac',
            },
        ],
        functionParameter: {
            actualRange: { name: 'фактический интервал ', detail: 'Интервал данных, который содержит результаты наблюдений, подлежащие сравнению с ожидаемыми значениями.' },
            expectedRange: { name: 'ожидаемый интервал ', detail: 'Интервал данных, который содержит отношение произведений итогов по строкам и столбцам к общему итогу.' },
        },
    },
    CONFIDENCE: {
        description: 'Возвращает доверительный интервал для среднего генеральной совокупности с нормальным распределением',
        abstract: 'Возвращает доверительный интервал для среднего генеральной совокупности с нормальным распределением',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B4%D0%BE%D0%B2%D0%B5%D1%80%D0%B8%D1%82-75ccc007-f77c-4343-bc14-673642091ad6',
            },
        ],
        functionParameter: {
            alpha: { name: 'альфа', detail: 'Уровень значимости, используемый для вычисления доверительного уровня. Доверительный уровень равен 100*(1 - альфа) процентам или, иными словами, значение аргумента "альфа", равное 0,05, означает 95-процентный доверительный уровень.' },
            standardDev: { name: 'стандартное отклонение', detail: 'Стандартное отклонение генеральной совокупности для диапазона данных, предполагается известным.' },
            size: { name: 'размер', detail: 'Размер выборки.' },
        },
    },
    COVAR: {
        description: 'Возвращает ковариацию, т. е. среднее произведений отклонений для каждой пары точек в двух наборах данных.',
        abstract: 'Возвращает ковариацию',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%BA%D0%BE%D0%B2%D0%B0%D1%80-50479552-2c03-4daf-bd71-a5ab88b2db03',
            },
        ],
        functionParameter: {
            array1: { name: 'массив1', detail: 'Первый диапазон ячеек с целыми числами.' },
            array2: { name: 'массив2', detail: ' Второй диапазон ячеек с целыми числами.' },
        },
    },
    CRITBINOM: {
        description: 'Возвращает наименьшее значение, для которого интегральное биномиальное распределение больше или равно заданному критерию',
        abstract: 'Возвращает наименьшее значение, для которого интегральное биномиальное распределение больше или равно заданному критерию',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%BA%D1%80%D0%B8%D1%82%D0%B1%D0%B8%D0%BD%D0%BE%D0%BC-eb6b871d-796b-4d21-b69b-e4350d5f407b',
            },
        ],
        functionParameter: {
            trials: { name: 'число испытаний', detail: 'Число испытаний Бернулли.' },
            probabilityS: { name: 'вероятность успеха ', detail: 'Вероятность успеха каждого испытания. ' },
            alpha: { name: 'альфа', detail: ' Значение критерия.' },
        },
    },
    EXPONDIST: {
        description: 'Возвращает экспоненциальное распределение',
        abstract: 'Возвращает экспоненциальное распределение',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D1%8D%D0%BA%D1%81%D0%BF%D1%80%D0%B0%D1%81%D0%BF-68ab45fd-cd6d-4887-9770-9357eb8ee06a',
            },
        ],
        functionParameter: {
            x: { name: 'x', detail: 'Значение, для которого требуется вычислить распределение.' },
            lambda: { name: 'лямбла', detail: 'Значение параметра.' },
            cumulative: { name: 'накопительная', detail: 'Логическое значение, определяющее форму экспоненциальной функции, которую следует использовать. Если аргумент "интегральная" имеет значение ИСТИНА, функция ЭКСПРАСП возвращает интегральную функцию распределения; если он имеет значение ЛОЖЬ, возвращается функция плотности распределения.' },
        },
    },
    FDIST: {
        description: 'Возвращает правый хвост F-распределения вероятности для двух наборов данных',
        abstract: 'Возвращает правый хвост F-распределения вероятности для двух наборов данных',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-f%D1%80%D0%B0%D1%81%D0%BF-ecf76fba-b3f1-4e7d-a57e-6a5b7460b786',
            },
        ],
        functionParameter: {
            x: { name: 'x', detail: 'Значение, для которого вычисляется функция.' },
            degFreedom1: { name: 'степени свободы1', detail: 'Числитель степеней свободы.' },
            degFreedom2: { name: 'степени свободы2', detail: 'Знаменатель степеней свободы.' },
        },
    },
    FINV: {
        description: 'Возвращает значение, обратное (правостороннему) F-распределению вероятностей',
        abstract: 'Возвращает значение, обратное (правостороннему) F-распределению вероятностей',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-f%D1%80%D0%B0%D1%81%D0%BF%D0%BE%D0%B1%D1%80-4d46c97c-c368-4852-bc15-41e8e31140b1',
            },
        ],
        functionParameter: {
            probability: { name: 'вероятность', detail: 'Вероятность, связанная с интегральным F-распределением.' },
            degFreedom1: { name: 'степени свободы1', detail: 'Числитель степеней свободы.' },
            degFreedom2: { name: 'степени свободы2', detail: 'Знаменатель степеней свободы.' },
        },
    },
    FTEST: {
        description: 'Возвращает результат F-теста',
        abstract: 'Возвращает результат F-теста',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D1%84%D1%82%D0%B5%D1%81%D1%82-4c9e1202-53fe-428c-a737-976f6fc3f9fd',
            },
        ],
        functionParameter: {
            array1: { name: 'массив1', detail: 'Первый массив или диапазон данных.' },
            array2: { name: 'массив2', detail: 'Второй массив или диапазон данных.' },
        },
    },
    GAMMADIST: {
        description: 'Возвращает гамма-распределение',
        abstract: 'Возвращает гамма-распределение',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B3%D0%B0%D0%BC%D0%BC%D0%B0%D1%80%D0%B0%D1%81%D0%BF-7327c94d-0f05-4511-83df-1dd7ed23e19e',
            },
        ],
        functionParameter: {
            x: { name: 'x', detail: 'Значение, для которого требуется вычислить распределение.' },
            alpha: { name: 'альфа', detail: 'Параметр распределения.' },
            beta: { name: 'бета', detail: 'Параметр распределения.' },
            cumulative: { name: 'накопительная', detail: 'Логическое значение, определяющее форму функции. Если аргумент "интегральная" имеет значение ИСТИНА, функция ГАММАРАСП возвращает интегральную функцию распределения; если этот аргумент имеет значение ЛОЖЬ, возвращается функция плотности распределения вероятности.' },
        },
    },
    GAMMAINV: {
        description: 'Возвращает обратное гамма-кумулятивное распределение',
        abstract: 'Возвращает обратное гамма-кумулятивное распределение',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B3%D0%B0%D0%BC%D0%BC%D0%B0%D0%BE%D0%B1%D1%80-06393558-37ab-47d0-aa63-432f99e7916d',
            },
        ],
        functionParameter: {
            probability: { name: 'вероятность', detail: 'Вероятность, связанная с гамма-распределением.' },
            alpha: { name: 'альфа', detail: 'Параметр распределения.' },
            beta: { name: 'бета', detail: 'Параметр распределения.' },
        },
    },
    HYPGEOMDIST: {
        description: 'Возвращает гипергеометрическое распределение',
        abstract: 'Возвращает гипергеометрическое распределение',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B3%D0%B8%D0%BF%D0%B5%D1%80%D0%B3%D0%B5%D0%BE%D0%BC%D0%B5%D1%82-23e37961-2871-4195-9629-d0b2c108a12e',
            },
        ],
        functionParameter: {
            sampleS: { name: 'число успехов в выборке', detail: 'Количество успешных испытаний в выборке.' },
            numberSample: { name: 'размер выборки', detail: 'Размер выборки.' },
            populationS: { name: 'число успехов в совокупности ', detail: 'Количество успешных испытаний в генеральной совокупности.' },
            numberPop: { name: 'размер совокупности', detail: 'Размер генеральной совокупности' },
            cumulative: { name: 'накопительная', detail: 'Логическое значение, определяющее вид функции. Если значение кумулятивного распределения равно TRUE, HYPGEOMDIST возвращает кумулятивную функцию распределения; если FALSE, функция возвращает функцию плотности вероятности.' },
        },
    },
    LOGINV: {
        description: 'Возвращает обратную функцию логнормального распределения',
        abstract: 'Возвращает обратную функцию логнормального распределения',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%BB%D0%BE%D0%B3%D0%BD%D0%BE%D1%80%D0%BC%D0%BE%D0%B1%D1%80-0bd7631a-2725-482b-afb4-de23df77acfe',
            },
        ],
        functionParameter: {
            probability: { name: 'вероятность', detail: 'Вероятность, связанная с логнормальным распределением.' },
            mean: { name: 'cреднее', detail: 'Среднее ln(x).' },
            standardDev: { name: 'стандартное отклонение', detail: 'Стандартное отклонение ln(x).' },
        },
    },
    LOGNORMDIST: {
        description: 'Возвращает интегральное логнормальное распределение',
        abstract: 'Возвращает интегральное логнормальное распределение',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%BB%D0%BE%D0%B3%D0%BD%D0%BE%D1%80%D0%BC%D1%80%D0%B0%D1%81%D0%BF-f8d194cb-9ee3-4034-8c75-1bdb3884100b',
            },
        ],
        functionParameter: {
            x: { name: 'x', detail: 'Значение, для которого требуется вычислить распределение.' },
            mean: { name: 'cреднее', detail: 'Среднее ln(x).' },
            standardDev: { name: 'стандартное отклонение', detail: 'Стандартное отклонение ln(x).' },
            cumulative: { name: 'накопительная', detail: 'Логическое значение, определяющее форму функции. Если кумулятивная функция TRUE, LOGNORM.DIST возвращает кумулятивную функцию распределения; если FALSE, возвращает функцию плотности вероятности.' },
        },
    },
    MODE: {
        description: 'Возвращает наиболее часто встречающееся или повторяющееся значение в массиве или диапазоне данных',
        abstract: 'Возвращает наиболее часто встречающееся или повторяющееся значение в массиве или диапазоне данных.',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%BC%D0%BE%D0%B4%D0%B0-e45192ce-9122-4980-82ed-4bdc34973120',
            },
        ],
        functionParameter: {
            number1: { name: 'число1', detail: 'Первый числовой аргумент, для которого требуется вычислить моду.' },
            number2: { name: 'число2', detail: 'От 1 до 255 числовых аргументов, для которых вычисляется мода. Вместо аргументов, разделенных точкой с запятой, можно воспользоваться массивом или ссылкой на массив.' },
        },
    },
    NEGBINOMDIST: {
        description: 'Возвращает отрицательное биномиальное распределение',
        abstract: 'Возвращает отрицательное биномиальное распределение',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%BE%D1%82%D1%80%D0%B1%D0%B8%D0%BD%D0%BE%D0%BC%D1%80%D0%B0%D1%81%D0%BF-f59b0a37-bae2-408d-b115-a315609ba714',
            },
        ],
        functionParameter: {
            numberF: { name: 'число неудач', detail: 'Количество неудачных испытаний.' },
            numberS: { name: 'число успехов', detail: ' Пороговое значение числа успешных испытаний.' },
            probabilityS: { name: 'вероятность успеха ', detail: 'Вероятность успеха.' },
            cumulative: { name: 'накопительная', detail: 'Логическое значение, определяющее форму функции. Если кумулятивная функция TRUE, NEGBINOMDIST возвращает кумулятивную функцию распределения; если FALSE, возвращает функцию плотности вероятности.' },
        },
    },
    NORMDIST: {
        description: 'Возвращает нормальное распределение для указанного среднего и стандартного отклонения',
        abstract: 'Возвращает нормальное распределение для указанного среднего и стандартного отклонения',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%BD%D0%BE%D1%80%D0%BC%D1%80%D0%B0%D1%81%D0%BF-126db625-c53e-4591-9a22-c9ff422d6d58',
            },
        ],
        functionParameter: {
            x: { name: 'x', detail: 'Значение, для которого требуется вычислить распределение.' },
            mean: { name: 'cреднее', detail: 'Среднее ln(x).' },
            standardDev: { name: 'стандартное отклонение', detail: 'Стандартное отклонение ln(x).' },
            cumulative: { name: 'накопительная', detail: 'Логическое значение, определяющее форму функции. Если кумулятивная функция TRUE, NORMDIST возвращает кумулятивную функцию распределения; если FALSE, возвращает функцию плотности вероятности.' },
        },
    },
    NORMINV: {
        description: 'Возвращает обратное нормальное распределение для указанного среднего и стандартного отклонения.',
        abstract: 'Возвращает обратное нормальное распределение для указанного среднего и стандартного отклонения.',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%BD%D0%BE%D1%80%D0%BC%D0%BE%D0%B1%D1%80-87981ab8-2de0-4cb0-b1aa-e21d4cb879b8',
            },
        ],
        functionParameter: {
            probability: { name: 'вероятность', detail: 'Вероятность, соответствующая нормальному распределению.' },
            mean: { name: 'cреднее', detail: 'Среднее ln(x).' },
            standardDev: { name: 'стандартное отклонение', detail: 'Стандартное отклонение ln(x).' },
        },
    },
    NORMSDIST: {
        description: 'Возвращает стандартное нормальное интегральное распределение',
        abstract: 'Возвращает стандартное нормальное интегральное распределение',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%BD%D0%BE%D1%80%D0%BC%D1%81%D1%82%D1%80%D0%B0%D1%81%D0%BF-463369ea-0345-445d-802a-4ff0d6ce7cac',
            },
        ],
        functionParameter: {
            z: { name: 'z', detail: 'Значение, для которого требуется вычислить распределение.' },
        },
    },
    NORMSINV: {
        description: 'Возвращает обратное значение стандартного нормального распределения',
        abstract: 'Возвращает обратное значение стандартного нормального распределения',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%BD%D0%BE%D1%80%D0%BC%D1%81%D1%82%D0%BE%D0%B1%D1%80-8d1bce66-8e4d-4f3b-967c-30eed61f019d',
            },
        ],
        functionParameter: {
            probability: { name: 'вероятность', detail: 'Вероятность, соответствующая нормальному распределению.' },
        },
    },
    PERCENTILE: {
        description: 'Возвращает k-ю персентиль для значений из интервала',
        abstract: 'Возвращает k-ю персентиль для значений из интервала',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%BF%D0%B5%D1%80%D1%81%D0%B5%D0%BD%D1%82%D0%B8%D0%BB%D1%8C-91b43a53-543c-4708-93de-d626debdddca',
            },
        ],
        functionParameter: {
            array: { name: 'массив', detail: 'Массив или диапазон данных, который определяет относительное положение.' },
            k: { name: 'k', detail: 'Значение процентили в интервале от 0 до 1, включая эти числа.' },
        },
    },
    PERCENTRANK: {
        description: 'Возвращает ранг значения в наборе данных в процентах от набора данных',
        abstract: 'Возвращает ранг значения в наборе данных в процентах от набора данных',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D0%BF%D1%80%D0%BE%D1%86%D0%B5%D0%BD%D1%82%D1%80%D0%B0%D0%BD%D0%B3-%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%BF%D1%80%D0%BE%D1%86%D0%B5%D0%BD%D1%82%D1%80%D0%B0%D0%BD%D0%B3-f1b5836c-9619-4847-9fc9-080ec9024442',
            },
        ],
        functionParameter: {
            array: { name: 'массив', detail: 'Массив или диапазон данных, который определяет относительное положение.' },
            x: { name: 'x', detail: 'Значение, для которого требуется узнать ранг в массиве.' },
            significance: { name: 'точность', detail: 'Значение, определяющее количество значимых цифр для возвращаемого процентного значения. Если этот аргумент опущен, для функции ПРОЦЕНТРАНГ используются три цифры (0,xxx).' },
        },
    },
    POISSON: {
        description: 'Возвращает распределение Пуассона',
        abstract: 'Возвращает распределение Пуассона',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%BF%D1%83%D0%B0%D1%81%D1%81%D0%BE%D0%BD-d81f7294-9d7c-4f75-bc23-80aa8624173a',
            },
        ],
        functionParameter: {
            x: { name: 'x', detail: 'Значение, для которого требуется вычислить распределение.' },
            mean: { name: 'cреднее', detail: 'Среднее ln(x).' },
            cumulative: { name: 'накопительная', detail: 'Логическое значение, определяющее форму функции. Если кумулятивная функция TRUE, POISSON возвращает кумулятивную функцию распределения; если FALSE, он возвращает функцию плотности вероятности.' },
        },
    },
    QUARTILE: {
        description: 'Возвращает квартиль множества данных',
        abstract: 'Возвращает квартиль множества данных',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%BA%D0%B2%D0%B0%D1%80%D1%82%D0%B8%D0%BB%D1%8C-93cf8f62-60cd-4fdb-8a92-8451041e1a2a',
            },
        ],
        functionParameter: {
            array: { name: 'массив', detail: 'Массив или диапазон ячеек с числовыми значениями, для которых определяется значение квартиля.' },
            quart: { name: 'часть', detail: 'Значение, которое требуется вернуть.' },
        },
    },
    RANK: {
        description: 'Возвращает ранг числа в списке чисел',
        abstract: 'Возвращает ранг числа в списке чисел',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D1%80%D0%B0%D0%BD%D0%B3-6a2fc49d-1831-4a03-9d8c-c279cf99f723',
            },
        ],
        functionParameter: {
            number: { name: 'число', detail: 'Число, для которого определяется ранг.' },
            ref: { name: 'cсылка', detail: 'Ссылка на список чисел. Нечисловые значения в ссылке игнорируются.' },
            order: { name: 'порядок', detail: 'Число, определяющее способ упорядочения. Если значение аргумента "порядок" равно 0 или опущено, ранг числа определяется в Microsoft Excel так, как если бы ссылка была списком, отсортированным в порядке убывания. Если значение аргумента "порядок" — любое число, кроме нуля, то ранг числа определяется в Microsoft Excel так, как если бы ссылка была списком, отсортированным в порядке возрастания.' },
        },
    },
    STDEV: {
        description: 'Оценивает стандартное отклонение по выборке. Стандартное отклонение — это мера того, насколько широко разбросаны точки данных относительно их среднего',
        abstract: 'Оценивает стандартное отклонение по выборке',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D1%81%D1%82%D0%B0%D0%BD%D0%B4%D0%BE%D1%82%D0%BA%D0%BB%D0%BE%D0%BD-51fecaaa-231e-4bbb-9230-33650a72c9b0',
            },
        ],
        functionParameter: {
            number1: { name: 'число1', detail: 'Первый числовой аргумент, соответствующий выборке из генеральной совокупности.' },
            number2: { name: 'число2', detail: 'Числовые аргументы 2—255, соответствующие выборке из генеральной совокупности. Вместо аргументов, разделенных точкой с запятой, можно использовать массив или ссылку на массив.' },
        },
    },
    STDEVP: {
        description: 'Вычисляет стандартное отклонение по генеральной совокупности',
        abstract: 'Вычисляет стандартное отклонение по генеральной совокупности',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D1%81%D1%82%D0%B0%D0%BD%D0%B4%D0%BE%D1%82%D0%BA%D0%BB%D0%BE%D0%BD%D0%BF-1f7c1c88-1bec-4422-8242-e9f7dc8bb195',
            },
        ],
        functionParameter: {
            number1: { name: 'число1', detail: 'Первый числовой аргумент, соответствующий генеральной совокупности.' },
            number2: { name: 'число2', detail: 'Числовые аргументы 2—255, соответствующие генеральной совокупности. Вместо аргументов, разделенных точкой с запятой, можно использовать массив или ссылку на массив.' },
        },
    },
    TDIST: {
        description: 'Возвращает процентные точки (вероятность) для t-распределения Стьюдента',
        abstract: 'Возвращает процентные точки (вероятность) для t-распределения Стьюдента',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D1%81%D1%82%D1%8C%D1%8E%D0%B4%D1%80%D0%B0%D1%81%D0%BF-630a7695-4021-4853-9468-4a1f9dcdd192',
            },
        ],
        functionParameter: {
            x: { name: 'x', detail: 'Числовое значение, для которого требуется вычислить распределение.' },
            degFreedom: { name: 'cтепени свободы', detail: 'Целое, указывающее число степеней свободы.' },
            tails: { name: 'хвосты', detail: 'Определяет количество возвращаемых хвостов распределения. Если значение "хвосты" = 1, функция TDIST возвращает одностороннее распределение. Если значение "хвосты" = 2, функция TDIST возвращает двустороннее распределение.' },
        },
    },
    TINV: {
        description: 'Возвращает двустороннее обратное t-распределения Стьюдента',
        abstract: 'Возвращает двустороннее обратное t-распределения Стьюдента',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D1%81%D1%82%D1%8C%D1%8E%D0%B4%D1%80%D0%B0%D1%81%D0%BF%D0%BE%D0%B1%D1%80-a7c85b9d-90f5-41fe-9ca5-1cd2f3e1ed7c',
            },
        ],
        functionParameter: {
            probability: { name: 'вероятность', detail: 'Вероятность, соответствующая двустороннему распределению Стьюдента.' },
            degFreedom: { name: 'cтепени свободы', detail: 'Целое, указывающее число степеней свободы.' },
        },
    },
    TTEST: {
        description: 'Возвращает вероятность, соответствующую критерию Стьюдента',
        abstract: 'Возвращает вероятность, соответствующую критерию Стьюдента',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D1%82%D1%82%D0%B5%D1%81%D1%82-1696ffc1-4811-40fd-9d13-a0eaad83c7ae',
            },
        ],
        functionParameter: {
            array1: { name: 'массив1', detail: 'Первый массив или диапазон данных.' },
            array2: { name: 'массив2', detail: 'Второй массив или диапазон данных.' },
            tails: { name: 'хвосты', detail: 'Число хвостов распределения. Если значение "хвосты" = 1, функция TTEST возвращает одностороннее распределение. Если значение "хвосты" = 2, функция TTEST возвращает двустороннее распределение.' },
            type: { name: 'тип', detail: 'Вид выполняемого t-теста.' },
        },
    },
    VAR: {
        description: 'Оценивает дисперсию по выборке',
        abstract: 'Оценивает дисперсию по выборке',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B4%D0%B8%D1%81%D0%BF-1f2b7ab2-954d-4e17-ba2c-9e58b15a7da2',
            },
        ],
        functionParameter: {
            number1: { name: 'число1', detail: 'Первый числовой аргумент, соответствующий выборке из генеральной совокупности.' },
            number2: { name: 'число2', detail: 'Числовые аргументы 2—255, соответствующие выборке из генеральной совокупности.' },
        },
    },
    VARP: {
        description: 'Вычисляет дисперсию для генеральной совокупности',
        abstract: 'Вычисляет дисперсию для генеральной совокупности',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B4%D0%B8%D1%81%D0%BF%D1%80-26a541c4-ecee-464d-a731-bd4c575b1a6b',
            },
        ],
        functionParameter: {
            number1: { name: 'число1', detail: 'Первый числовой аргумент, соответствующий генеральной совокупности.' },
            number2: { name: 'число2', detail: 'Числовые аргументы 2—255, соответствующие генеральной совокупности.' },
        },
    },
    WEIBULL: {
        description: 'Возвращает распределение Вейбулла',
        abstract: 'Возвращает распределение Вейбулла',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B2%D0%B5%D0%B9%D0%B1%D1%83%D0%BB%D0%BB-b83dc2c6-260b-4754-bef2-633196f6fdcc',
            },
        ],
        functionParameter: {
            x: { name: 'x', detail: 'Значение, для которого требуется вычислить распределение.' },
            alpha: { name: 'альфа', detail: 'Параметр распределения.' },
            beta: { name: 'бета', detail: 'Параметр распределения.' },
            cumulative: { name: 'накопительная', detail: 'Логическое значение, определяющее форму функции. Если кумулятивная функция TRUE, WEIBULL возвращает кумулятивную функцию распределения; если FALSE, он возвращает функцию плотности вероятности.' },
        },
    },
    ZTEST: {
        description: 'Возвращает одностороннее значение вероятности z-теста',
        abstract: 'Возвращает одностороннее значение вероятности z-теста',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-z%D1%82%D0%B5%D1%81%D1%82-8f33be8a-6bd6-4ecc-8e3a-d9a4420c4a6a',
            },
        ],
        functionParameter: {
            array: { name: 'массив', detail: 'Массив или диапазон данных, с которыми сравнивается x.' },
            x: { name: 'x', detail: 'Проверяемое значение.' },
            sigma: { name: 'сигма', detail: 'Известное стандартное отклонение генеральной совокупности. Если этот аргумент опущен, используется стандартное отклонение выборки.' },
        },
    },
};

export default locale;
