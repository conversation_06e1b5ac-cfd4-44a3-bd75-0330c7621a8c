{"name": "@univerjs/sheets-hyper-link", "version": "0.10.0", "private": false, "description": "", "author": "DreamNum <<EMAIL>>", "license": "Apache-2.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "homepage": "https://univer.ai", "repository": {"type": "git", "url": "https://github.com/dream-num/univer"}, "bugs": {"url": "https://github.com/dream-num/univer/issues"}, "keywords": [], "exports": {".": "./src/index.ts", "./*": "./src/*", "./facade": "./src/facade/index.ts"}, "main": "./src/index.ts", "types": "./lib/types/index.d.ts", "publishConfig": {"access": "public", "main": "./lib/es/index.js", "module": "./lib/es/index.js", "exports": {".": {"import": "./lib/es/index.js", "require": "./lib/cjs/index.js", "types": "./lib/types/index.d.ts"}, "./*": {"import": "./lib/es/*", "require": "./lib/cjs/*", "types": "./lib/types/index.d.ts"}, "./facade": {"import": "./lib/es/facade.js", "require": "./lib/cjs/facade.js", "types": "./lib/types/facade/index.d.ts"}, "./lib/facade": {"import": "./lib/es/facade.js", "require": "./lib/cjs/facade.js", "types": "./lib/types/facade/index.d.ts"}, "./lib/*": "./lib/*"}}, "directories": {"lib": "lib"}, "files": ["lib"], "scripts": {"test": "vitest run", "test:watch": "vitest", "coverage": "vitest run --coverage", "lint:types": "tsc --noEmit", "build": "univer-cli build"}, "peerDependencies": {"rxjs": ">=7.0.0"}, "dependencies": {"@univerjs/core": "workspace:*", "@univerjs/docs": "workspace:*", "@univerjs/engine-formula": "workspace:*", "@univerjs/sheets": "workspace:*"}, "devDependencies": {"@univerjs-infra/shared": "workspace:*", "rxjs": "^7.8.2", "typescript": "^5.8.3", "vite": "^7.0.6", "vitest": "^3.2.4"}}