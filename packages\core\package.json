{"name": "@univerjs/core", "version": "0.10.0", "private": false, "description": "Core library for Univer.", "author": "DreamNum <<EMAIL>>", "license": "Apache-2.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "homepage": "https://univer.ai", "repository": {"type": "git", "url": "https://github.com/dream-num/univer"}, "bugs": {"url": "https://github.com/dream-num/univer/issues"}, "keywords": ["univer"], "exports": {".": "./src/index.ts", "./*": "./src/*", "./facade": "./src/facade/index.ts"}, "main": "./src/index.ts", "types": "./lib/types/index.d.ts", "publishConfig": {"access": "public", "main": "./lib/es/index.js", "module": "./lib/es/index.js", "exports": {".": {"import": "./lib/es/index.js", "require": "./lib/cjs/index.js", "types": "./lib/types/index.d.ts"}, "./*": {"import": "./lib/es/*", "require": "./lib/cjs/*", "types": "./lib/types/index.d.ts"}, "./facade": {"import": "./lib/es/facade.js", "require": "./lib/cjs/facade.js", "types": "./lib/types/facade/index.d.ts"}, "./lib/facade": {"import": "./lib/es/facade.js", "require": "./lib/cjs/facade.js", "types": "./lib/types/facade/index.d.ts"}, "./lib/*": "./lib/*"}}, "directories": {"lib": "lib"}, "files": ["lib"], "scripts": {"test": "vitest run", "test:watch": "vitest", "coverage": "vitest run --coverage", "lint:types": "tsc --noEmit", "build": "univer-cli build"}, "peerDependencies": {"@wendellhu/redi": "1.0.0", "rxjs": ">=7.0.0"}, "dependencies": {"@univerjs/protocol": "0.1.46", "@univerjs/themes": "workspace:*", "@wendellhu/redi": "1.0.0", "async-lock": "^1.4.1", "dayjs": "^1.11.13", "fast-diff": "1.3.0", "kdbush": "^4.0.2", "lodash-es": "^4.17.21", "nanoid": "5.1.5", "numfmt": "^3.2.3", "ot-json1": "^1.0.2", "rbush": "^4.0.1"}, "devDependencies": {"@types/async-lock": "^1.4.2", "@types/lodash-es": "^4.17.12", "@types/rbush": "^4.0.0", "@univerjs-infra/shared": "workspace:*", "rxjs": "^7.8.2", "typescript": "^5.8.3", "vite": "^7.0.6", "vitest": "^3.2.4"}}