{"name": "univer", "type": "module", "version": "0.10.0", "private": true, "packageManager": "pnpm@10.13.1", "author": "DreamNum Co., Ltd. <<EMAIL>>", "license": "Apache-2.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "homepage": "https://univer.ai", "repository": {"type": "git", "url": "https://github.com/dream-num/univer"}, "bugs": {"url": "https://github.com/dream-num/univer/issues"}, "engines": {"node": ">=20", "pnpm": ">=10"}, "scripts": {"prepare": "husky", "pre-commit": "lint-staged", "dev": "pnpm --filter univer-examples dev:demo -- --host 0.0.0.0", "dev:libs": "pnpm --filter univer-examples dev:demo-libs", "dev:e2e": "pnpm --filter univer-examples dev:e2e", "use:react16": "tsx ./scripts/react-version-manager.ts --react=16", "use:react19": "tsx ./scripts/react-version-manager.ts --react=19", "lint:types": "turbo lint:types", "test": "turbo test -- --passWithNoTests", "coverage": "turbo coverage -- --passWithNoTests", "build": "turbo build --concurrency=30% --filter=!./common/*", "build:ci": "turbo build --concurrency=100% --filter=!./common/*", "build:filter": "pnpm --filter @univerjs/sheets-filter --filter @univerjs/sheets-filter-ui --filter @univerjs/sheets-table-ui build", "build:demo": "pnpm --filter univer-examples build:demo", "build:e2e": "pnpm --filter univer-examples build:e2e", "serve:e2e": "serve ./examples/local", "test:e2e": "playwright test", "lint": "eslint .", "storybook:dev": "pnpm --filter @univerjs/storybook dev:storybook", "storybook:build": "pnpm --filter @univerjs/storybook build:storybook", "release": "release-it"}, "devDependencies": {"@antfu/eslint-config": "5.0.0", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint-react/eslint-plugin": "^1.52.3", "@playwright/test": "^1.54.1", "@release-it-plugins/workspaces": "^5.0.3", "@release-it/conventional-changelog": "^10.0.1", "@types/fs-extra": "^11.0.4", "@types/node": "^24.1.0", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@univerjs-infra/shared": "workspace:*", "@univerjs/design": "workspace:*", "@vitejs/plugin-react": "^4.7.0", "eslint": "9.32.0", "eslint-plugin-format": "^1.0.1", "eslint-plugin-header": "^3.1.1", "eslint-plugin-jsdoc": "^51.4.1", "eslint-plugin-no-barrel-import": "^0.0.2", "eslint-plugin-no-penetrating-import": "^0.0.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "fs-extra": "^11.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "posthog-node": "^5.6.0", "react": "19.1.1", "react-dom": "19.1.1", "release-it": "^19.0.4", "serve": "^14.2.4", "tailwindcss": "3.4.17", "tsx": "^4.20.3", "turbo": "^2.5.5", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}, "resolutions": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6", "react": "19.1.1", "react-dom": "19.1.1"}, "lint-staged": {"*": "eslint --fix"}}