{"name": "@univerjs/storybook", "version": "0.10.0", "private": true, "description": "Some infrastructures for univerjs", "author": "DreamNum <<EMAIL>>", "license": "Apache-2.0", "homepage": "https://github.com/dream-num/univer", "repository": {"type": "git", "url": "https://github.com/dream-num/univer.git"}, "keywords": [], "scripts": {"dev:storybook": "storybook dev -p 6006 --no-open", "build:storybook": "storybook build"}, "dependencies": {"@chromatic-com/storybook": "^4.0.1", "@storybook/addon-docs": "^9.0.18", "@storybook/addon-links": "^9.0.18", "@storybook/addon-styling-webpack": "^2.0.0", "@storybook/addon-webpack5-compiler-swc": "^3.0.0", "@storybook/icons": "^1.4.0", "@storybook/react": "^9.0.18", "@storybook/react-webpack5": "^9.0.18", "@univerjs/core": "workspace:*", "@univerjs/design": "workspace:*", "@univerjs/themes": "workspace:*", "@univerjs/ui": "workspace:*", "css-loader": "^7.1.2", "postcss-loader": "^8.1.1", "storybook": "^9.0.18", "storybook-addon-swc": "^1.2.0", "style-loader": "^4.0.0", "tsconfig-paths-webpack-plugin": "^4.2.0", "typescript": "^5.8.3"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@univerjs-infra/shared": "workspace:*", "fs-extra": "^11.3.0", "postcss": "^8.5.6", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7"}}