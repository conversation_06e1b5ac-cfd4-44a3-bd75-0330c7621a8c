{"name": "@univerjs/sheets-note-ui", "version": "0.10.0", "private": false, "description": "Univer sheets note UI plugin", "author": "DreamNum <<EMAIL>>", "license": "Apache-2.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "homepage": "https://univer.ai", "repository": {"type": "git", "url": "https://github.com/dream-num/univer"}, "bugs": {"url": "https://github.com/dream-num/univer/issues"}, "keywords": [], "exports": {".": "./src/index.ts", "./*": "./src/*", "./locale/*": "./src/locale/*.ts", "./facade": "./src/facade/index.ts"}, "main": "./lib/index.js", "module": "./lib/index.js", "types": "./lib/index.d.ts", "files": ["lib"], "publishConfig": {"access": "public", "main": "./lib/es/index.js", "module": "./lib/es/index.js", "exports": {".": {"import": "./lib/es/index.js", "require": "./lib/cjs/index.js", "types": "./lib/types/index.d.ts"}, "./*": {"import": "./lib/es/*", "require": "./lib/cjs/*", "types": "./lib/types/index.d.ts"}, "./locale/*": {"import": "./lib/es/locale/*.js", "require": "./lib/cjs/locale/*.js", "types": "./lib/types/locale/*.d.ts"}, "./lib/*": "./lib/*"}}, "scripts": {"test": "vitest run", "test:watch": "vitest", "build": "univer-cli build"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}, "dependencies": {"@univerjs/core": "workspace:*", "@univerjs/design": "workspace:*", "@univerjs/engine-render": "workspace:*", "@univerjs/icons": "^0.4.6", "@univerjs/sheets": "workspace:*", "@univerjs/sheets-note": "workspace:*", "@univerjs/sheets-ui": "workspace:*", "@univerjs/ui": "workspace:*", "rxjs": "^7.8.2"}, "devDependencies": {"@types/react": "^18.0.15", "@types/react-dom": "^18.0.6", "postcss": "^8.5.6", "tailwindcss": "3.4.17", "typescript": "^5.8.3", "vite": "^7.0.6", "vitest": "^3.2.4"}}