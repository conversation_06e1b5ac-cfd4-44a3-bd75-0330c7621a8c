{"name": "@univerjs/docs-ui", "version": "0.10.0", "private": false, "description": "Univer normal ui-plugin-docs", "author": "DreamNum <<EMAIL>>", "license": "Apache-2.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "homepage": "https://univer.ai", "repository": {"type": "git", "url": "https://github.com/dream-num/univer"}, "bugs": {"url": "https://github.com/dream-num/univer/issues"}, "keywords": ["univer"], "exports": {".": "./src/index.ts", "./*": "./src/*", "./locale/*": "./src/locale/*.ts", "./facade": "./src/facade/index.ts"}, "main": "./src/index.ts", "types": "./lib/types/index.d.ts", "publishConfig": {"access": "public", "main": "./lib/es/index.js", "module": "./lib/es/index.js", "exports": {".": {"import": "./lib/es/index.js", "require": "./lib/cjs/index.js", "types": "./lib/types/index.d.ts"}, "./*": {"import": "./lib/es/*", "require": "./lib/cjs/*", "types": "./lib/types/index.d.ts"}, "./locale/*": {"import": "./lib/es/locale/*.js", "require": "./lib/cjs/locale/*.js", "types": "./lib/types/locale/*.d.ts"}, "./facade": {"import": "./lib/es/facade.js", "require": "./lib/cjs/facade.js", "types": "./lib/types/facade/index.d.ts"}, "./lib/facade": {"import": "./lib/es/facade.js", "require": "./lib/cjs/facade.js", "types": "./lib/types/facade/index.d.ts"}, "./lib/*": "./lib/*"}}, "directories": {"lib": "lib"}, "files": ["lib"], "scripts": {"test": "vitest run", "test:watch": "vitest", "coverage": "vitest run --coverage", "lint:types": "tsc --noEmit", "build": "univer-cli build"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}, "dependencies": {"@univerjs/core": "workspace:*", "@univerjs/design": "workspace:*", "@univerjs/docs": "workspace:*", "@univerjs/drawing": "workspace:*", "@univerjs/engine-render": "workspace:*", "@univerjs/icons": "^0.4.6", "@univerjs/ui": "workspace:*"}, "devDependencies": {"@univerjs-infra/shared": "workspace:*", "@univerjs/docs-drawing": "workspace:*", "jest-canvas-mock": "^2.5.2", "jsdom": "^26.1.0", "postcss": "^8.5.6", "react": "18.3.1", "rxjs": "^7.8.2", "tailwindcss": "3.4.17", "typescript": "^5.8.3", "vite": "^7.0.6", "vitest": "^3.2.4"}}