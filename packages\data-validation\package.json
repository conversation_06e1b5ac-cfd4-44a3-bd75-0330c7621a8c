{"name": "@univerjs/data-validation", "version": "0.10.0", "private": false, "description": "Data validation library for Univer", "author": "DreamNum <<EMAIL>>", "license": "Apache-2.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "homepage": "https://univer.ai", "repository": {"type": "git", "url": "https://github.com/dream-num/univer"}, "bugs": {"url": "https://github.com/dream-num/univer/issues"}, "keywords": ["univer"], "exports": {".": "./src/index.ts", "./*": "./src/*"}, "main": "./src/index.ts", "types": "./lib/types/index.d.ts", "publishConfig": {"access": "public", "main": "./lib/es/index.js", "module": "./lib/es/index.js", "exports": {".": {"import": "./lib/es/index.js", "require": "./lib/cjs/index.js", "types": "./lib/types/index.d.ts"}, "./*": {"import": "./lib/es/*", "require": "./lib/cjs/*", "types": "./lib/types/index.d.ts"}, "./lib/*": "./lib/*"}}, "directories": {"lib": "lib"}, "files": ["lib"], "scripts": {"dev": "vite", "test": "vitest run", "test:watch": "vitest", "coverage": "vitest run --coverage", "build": "univer-cli build"}, "peerDependencies": {"rxjs": ">=7.0.0"}, "dependencies": {"@univerjs/core": "workspace:*"}, "devDependencies": {"@univerjs-infra/shared": "workspace:*", "@univerjs/core": "workspace:*", "@univerjs/protocol": "0.1.46", "@univerjs/sheets": "workspace:*", "rxjs": "^7.8.2", "typescript": "^5.8.3", "vite": "^7.0.6", "vitest": "^3.2.4"}}