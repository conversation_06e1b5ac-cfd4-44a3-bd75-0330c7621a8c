/**
 * Copyright 2023-present DreamNum Co., Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type enUS from './en-US';

const locale: typeof enUS = {
    DATE: {
        description: 'Возвращает порядковый номер определенной даты',
        abstract: 'Возвращает порядковый номер определенной даты',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D0%B4%D0%B0%D1%82%D0%B0-%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B4%D0%B0%D1%82%D0%B0-e36c0c8c-4104-49da-ab83-82328b832349',
            },
        ],
        functionParameter: {
            year: { name: 'год', detail: 'Значение аргумента год может содержать от одной до четырех цифр. Excel интерпретирует аргумент год в соответствии с используемой системой дат, используемой на вашем компьютере. По умолчанию в Microsoft Excel для Windows используется система дат 1900, то есть первой датой считается 1 января 1900 г.' },
            month: { name: 'месяц', detail: 'Положительное или отрицательное целое число в диапазоне от 1 (январь) до 12 (декабрь), представляющее месяц года.' },
            day: { name: 'день', detail: 'Положительное или отрицательное целое число в диапазоне от 1 до 31, представляющее день месяца.' },
        },
    },
    DATEDIF: {
        description: 'Вычисляет количество дней, месяцев или лет между двумя датами',
        abstract: 'Вычисляет количество дней, месяцев или лет между двумя датами',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%80%D0%B0%D0%B7%D0%BD%D0%B4%D0%B0%D1%82-25dba1a4-2812-480b-84dd-8b32a451b35c',
            },
        ],
        functionParameter: {
            startDate: { name: 'начальная дата', detail: 'Дата, представляющая первую или начальную дату заданного периода.' },
            endDate: { name: 'конечная дата', detail: 'Дата окончания периода.' },
            method: { name: 'тип', detail: 'Тип возвращаемых сведений.' },
        },
    },
    DATEVALUE: {
        description: 'Преобразует дату, которая хранится в виде текста, в порядковый номер',
        abstract: 'Преобразует дату, которая хранится в виде текста, в порядковый номер',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B4%D0%B0%D1%82%D0%B0%D0%B7%D0%BD%D0%B0%D1%87-df8b07d4-7761-4a93-bc33-b7471bbff252',
            },
        ],
        functionParameter: {
            dateText: { name: 'дата как текст', detail: 'Текст, представляющий дату в формате даты Excel, или ссылка на ячейку с таким текстом. Например, "30.01.2008" и "30-янв-2008" — это текстовые строки в кавычках, представляющие даты.' },
        },
    },
    DAY: {
        description: 'Возвращает день даты, заданной в числовом формате. День возвращается как целое число в диапазоне от 1 до 31',
        abstract: 'Возвращает день даты, заданной в числовом формате. День возвращается как целое число в диапазоне от 1 до 31',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B4%D0%B5%D0%BD%D1%8C-8a7d1cbb-6c7d-4ba1-8aea-25c134d03101',
            },
        ],
        functionParameter: {
            serialNumber: { name: 'дата в числовом формате', detail: 'Дата, которую необходимо найти. Даты вводятся с использованием функции ДАТА или как результат других формул и функций. Например, для указания даты 23 мая 2008 года следует воспользоваться выражением ДАТА(2008;5;23).' },
        },
    },
    DAYS: {
        description: 'Возвращает количество дней между двумя датами',
        abstract: 'Возвращает количество дней между двумя датами',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D0%B4%D0%BD%D0%B8-%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B4%D0%BD%D0%B8-57740535-d549-4395-8728-0f07bff0b9df',
            },
        ],
        functionParameter: {
            endDate: { name: 'конечная дата', detail: 'Начальная дата и конечная дата — две даты, количество дней между которыми необходимо вычислить.' },
            startDate: { name: 'начальная дата', detail: 'Начальная дата и конечная дата — две даты, количество дней между которыми необходимо вычислить.' },
        },
    },
    DAYS360: {
        description: 'Возвращает количество дней между двумя датами на основе 360-дневного года',
        abstract: 'Возвращает количество дней между двумя датами на основе 360-дневного года',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B4%D0%BD%D0%B5%D0%B9360-b9a509fd-49ef-407e-94df-0cbda5718c2a',
            },
        ],
        functionParameter: {
            startDate: { name: 'начальная дата', detail: 'Начальная дата и конечная дата — две даты, количество дней между которыми необходимо вычислить.' },
            endDate: { name: 'конечная дата', detail: 'Начальная дата и конечная дата — две даты, количество дней между которыми необходимо вычислить.' },
            method: { name: 'метод', detail: 'Логическое значение, которое определяет, какой метод, европейский или американский, необходимо использовать при вычислениях.' },
        },
    },
    EDATE: {
        description: 'Возвращает порядковый номер даты, отстоящей на заданное количество месяцев вперед или назад от заданной даты. Функция EDATE используется для вычисления срока погашения или даты платежа, приходящейся на тот же день месяца, что и дата выпуска',
        abstract: 'Возвращает порядковый номер даты, отстоящей на заданное количество месяцев вперед или назад от заданной даты',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B4%D0%B0%D1%82%D0%B0%D0%BC%D0%B5%D1%81-3c920eb2-6e66-44e7-a1f5-753ae47ee4f5',
            },
        ],
        functionParameter: {
            startDate: { name: 'начальная дата', detail: 'Даты должны быть введены с использованием функции ДАТА или как результат вычисления других формул и функций. Например, для указания даты 23 мая 2008 года следует воспользоваться выражением ДАТА(2008;5;23). Если ввести даты как текст, это может привести к возникновению проблем.' },
            months: { name: 'число месяцев', detail: 'Количество месяцев до или после даты "нач_дата". Положительное значение аргумента "число_месяцев" означает будущие даты; отрицательное значение — прошедшие даты.' },
        },
    },
    EOMONTH: {
        description: 'Возвращает порядковый номер последнего дня месяца, отстоящего на указанное количество месяцев от даты, указанной в аргументе "начальная дата"',
        abstract: 'Возвращает порядковый номер последнего дня месяца, отстоящего на указанное количество месяцев от даты, указанной в аргументе "начальная дата"',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%BA%D0%BE%D0%BD%D0%BC%D0%B5%D1%81%D1%8F%D1%86%D0%B0-7314ffa1-2bc9-4005-9d66-f49db127d628',
            },
        ],
        functionParameter: {
            startDate: { name: 'начальная дата', detail: 'Начальная дата.' },
            months: { name: 'число месяцев', detail: 'Количество месяцев до или после даты "начальная дата".' },
        },
    },
    EPOCHTODATE: {
        description: 'Преобразует Unix-время в секундах, миллисекундах или микросекундах в дату и время по всемирному координированному времени (UTC).',
        abstract: 'Преобразует Unix-время в секундах, миллисекундах или микросекундах в дату и время по всемирному координированному времени (UTC).',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.google.com/docs/answer/13193461?hl=ru',
            },
        ],
        functionParameter: {
            timestamp: { name: 'временная метка', detail: 'Unix-время в секундах, миллисекундах или микросекундах.' },
            unit: { name: 'единица', detail: 'Eдиница измерения времени во временной метке (значение по умолчанию: 1): \n1 - секунды. \n2 - миллисекунды.\n3 - микросекунды' },
        },
    },
    HOUR: {
        description: 'Возвращает час, соответствующий заданному времени. Час определяется как целое число в интервале от 0 до 23',
        abstract: 'Возвращает час, соответствующий заданному времени. Час определяется как целое число в интервале от 0 до 23',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D1%87%D0%B0%D1%81-a3afa879-86cb-4339-b1b5-2dd2d7310ac7',
            },
        ],
        functionParameter: {
            serialNumber: { name: 'дата в числовом формате', detail: 'Дата, которую необходимо найти. Даты вводятся с использованием функции ДАТА или как результат других формул и функций. Например, для указания даты 23 мая 2008 года следует воспользоваться выражением ДАТА(2008;5;23)' },
        },
    },
    ISOWEEKNUM: {
        description: 'Возвращает номер недели в году для определенной даты в соответствии со стандартами ISO',
        abstract: 'Возвращает номер недели в году для определенной даты в соответствии со стандартами ISO',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/isoweeknum-function-1c2d0afe-d25b-4ab1-8894-8d0520e90e0e',
            },
        ],
        functionParameter: {
            date: { name: 'дата', detail: 'Дата — это код даты и времени, с помощью которого в Microsoft Excel производятся вычисления над датами и промежутками времени.' },
        },
    },
    MINUTE: {
        description: 'Возвращает минуты, соответствующие аргументу время в числовом формате',
        abstract: 'Возвращает минуты, соответствующие аргументу время в числовом формате',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%BC%D0%B8%D0%BD%D1%83%D1%82%D1%8B-af728df0-05c4-4b07-9eed-a84801a60589',
            },
        ],
        functionParameter: {
            serialNumber: { name: 'дата в числовом формате', detail: 'Дата, которую необходимо найти. Даты вводятся с использованием функции ДАТА или как результат других формул и функций. Например, для указания даты 23 мая 2008 года следует воспользоваться выражением ДАТА(2008;5;23)' },
        },
    },
    MONTH: {
        description: 'Возвращает месяц для даты, заданной в числовом формате. Месяц возвращается как целое число в диапазоне от 1 (январь) до 12 (декабрь)',
        abstract: 'Возвращает месяц для даты, заданной в числовом формате',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D0%BC%D0%B5%D1%81%D1%8F%D1%86-%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%BC%D0%B5%D1%81%D1%8F%D1%86-579a2881-199b-48b2-ab90-ddba0eba86e8',
            },
        ],
        functionParameter: {
            serialNumber: { name: 'дата в числовом формате', detail: 'Дата месяца, который необходимо найти. Дата должна быть введена с использованием функции ДАТА либо как результат других формул или функций. Например, для указания даты 23 мая 2008 года следует воспользоваться выражением ДАТА(2008;5;23).' },
        },
    },
    NETWORKDAYS: {
        description: 'Возвращает количество рабочих дней между датами "начальная дата" и "конечная дата"',
        abstract: 'Возвращает количество рабочих дней между датами "начальная дата" и "конечная дата"',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%87%D0%B8%D1%81%D1%82%D1%80%D0%B0%D0%B1%D0%B4%D0%BD%D0%B8-%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D1%87%D0%B8%D1%81%D1%82%D1%80%D0%B0%D0%B1%D0%B4%D0%BD%D0%B8-48e717bf-a7a3-495f-969e-5005e3eb18e7',
            },
        ],
        functionParameter: {
            startDate: { name: 'начальная дата', detail: 'Начальная дата.' },
            endDate: { name: 'конечная дата', detail: 'Конечная дата.' },
            holidays: { name: 'праздники', detail: 'Список из одной или нескольких дат, которые требуется исключить из рабочего календаря, например государственные праздники. Список может представлять собой диапазон ячеек, содержащих даты, или константу массива, содержащую числа, которые представляют даты.' },
        },
    },
    NETWORKDAYS_INTL: {
        description: 'Возвращает количество рабочих дней между двумя датами с использованием параметров, определяющих, сколько в неделе выходных и какие дни являются выходными. Выходные и любые праздники не считаются рабочими днями',
        abstract: 'Возвращает количество рабочих дней между двумя датами с использованием параметров, определяющих, сколько в неделе выходных и какие дни являются выходными. Выходные и любые праздники не считаются рабочими днями.',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D1%87%D0%B8%D1%81%D1%82%D1%80%D0%B0%D0%B1%D0%B4%D0%BD%D0%B8-%D0%BC%D0%B5%D0%B6%D0%B4-a9b26239-4f20-46a1-9ab8-4e925bfd5e28',
            },
        ],
        functionParameter: {
            startDate: { name: 'начальная дата', detail: 'Начальная дата.' },
            endDate: { name: 'конечная дата', detail: 'Конечная дата.' },
            weekend: { name: 'выходные', detail: 'Указывает, какие дни недели являются выходными и не включаются в число рабочих дней между начальной и конечной датой.' },
            holidays: { name: 'праздники', detail: 'Список из одной или нескольких дат, которые требуется исключить из рабочего календаря, например государственные праздники. Список может представлять собой диапазон ячеек, содержащих даты, или константу массива, содержащую числа, которые представляют даты.' },
        },
    },
    NOW: {
        description: 'Возвращает текущую дату и время в числовом формате',
        abstract: 'Возвращает текущую дату и время в числовом формате',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D1%82%D0%B4%D0%B0%D1%82%D0%B0-3337fd29-145a-4347-b2e6-20c904739c46',
            },
        ],
        functionParameter: {
        },
    },
    SECOND: {
        description: 'Возвращает секунды, соответствующие аргументу время в числовом формате',
        abstract: 'Возвращает секунды, соответствующие аргументу время в числовом формате',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D1%81%D0%B5%D0%BA%D1%83%D0%BD%D0%B4%D1%8B-740d1cfc-553c-4099-b668-80eaa24e8af1',
            },
        ],
        functionParameter: {
            serialNumber: { name: 'дата в числовом формате', detail: 'Дата, которую необходимо найти. Даты вводятся с использованием функции ДАТА или как результат других формул и функций. Например, для указания даты 23 мая 2008 года следует воспользоваться выражением ДАТА(2008;5;23)' },
        },
    },
    TIME: {
        description: 'Возвращает десятичное число, представляющее определенное время',
        abstract: 'Возвращает десятичное число, представляющее определенное время',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B2%D1%80%D0%B5%D0%BC%D1%8F-9a5aff99-8f7d-4611-845e-747d0b8d5457',
            },
        ],
        functionParameter: {
            hour: { name: 'часы', detail: 'Число от нуля (0) до 32767, задающее часы. Если значение больше 23, оно делится на 24; остаток от деления будет соответствовать значению часов. Например, ВРЕМЯ(27;0;0) = ВРЕМЯ(3;0;0) = 0,125, то есть 03:00.' },
            minute: { name: 'минуты', detail: 'Число от 0 до 32767, задающее минуты. Если значение больше 59, оно будет пересчитано в часы и минуты. Например, ВРЕМЯ(0;750;0) = ВРЕМЯ(12;30;0) = 0,520833, то есть 12:30.' },
            second: { name: 'секунды', detail: 'Число от 0 до 32767, задающее секунды. Если значение больше 59, оно будет пересчитано в часы, минуты и секунды. Например, ВРЕМЯ(0;0;2000) = ВРЕМЯ(0;33;22) = 0,023148, то есть 12:33:20.' },
        },
    },
    TIMEVALUE: {
        description: 'Возвращает время в виде десятичного числа, представленное текстовой строкой',
        abstract: 'Возвращает время в виде десятичного числа, представленное текстовой строкой',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B2%D1%80%D0%B5%D0%BC%D0%B7%D0%BD%D0%B0%D1%87-0b615c12-33d8-4431-bf3d-f3eb6d186645',
            },
        ],
        functionParameter: {
            timeText: { name: 'время как текст', detail: ' Текстовая строка, представляющая время в любом из форматов времени Microsoft Excel; Например, текстовые строки "18:45 PM" и "18:45" в кавычках, которые представляют время.' },
        },
    },
    TO_DATE: {
        description: 'Преобразует число в значение даты',
        abstract: 'Преобразует число в значение даты',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.google.com/docs/answer/3094239?hl=ru&sjid=2155433538747546473-AP',
            },
        ],
        functionParameter: {
            value: { name: 'значение', detail: 'Число, которое необходимо преобразовать в дату, или ссылка на ячейку, содержащую такое число' },
        },
    },
    TODAY: {
        description: 'Возвращает серийный номер текущей даты',
        abstract: 'Возвращает серийный номер текущей даты',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D1%81%D0%B5%D0%B3%D0%BE%D0%B4%D0%BD%D1%8F-5eb3078d-a82c-4736-8930-2f51a028fdd9',
            },
        ],
        functionParameter: {
        },
    },
    WEEKDAY: {
        description: 'Возвращает день недели, соответствующий дате',
        abstract: 'Возвращает день недели, соответствующий дате',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B4%D0%B5%D0%BD%D1%8C%D0%BD%D0%B5%D0%B4-60e44483-2ed1-439f-8bd0-e404c190949a',
            },
        ],
        functionParameter: {
            serialNumber: { name: 'дата в числовом формате', detail: 'Порядковый номер, соответствующий дате, день недели для которой необходимо найти.' },
            returnType: { name: 'тип', detail: 'Число, определяющее тип возвращаемого значения.' },
        },
    },
    WEEKNUM: {
        description: 'Возвращает номер недели для определенной даты',
        abstract: 'Возвращает номер недели для определенной даты',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%BD%D0%BE%D0%BC%D0%BD%D0%B5%D0%B4%D0%B5%D0%BB%D0%B8-e5c43a03-b4ab-426c-b411-b18c13c75340',
            },
        ],
        functionParameter: {
            serialNumber: { name: 'дата в числовом формате', detail: 'Дата в течение недели' },
            returnType: { name: 'тип', detail: 'Неделя, на которую приходится первый четверг года, считается первой неделей, и для нее возвращается число 1' },
        },
    },
    WORKDAY: {
        description: 'Возвращает число, которое представляет дату, отстоящую на заданное количество рабочих дней вперед или назад от начальной даты',
        abstract: 'Возвращает число, которое представляет дату, отстоящую на заданное количество рабочих дней вперед или назад от начальной даты',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D1%80%D0%B0%D0%B1%D0%B4%D0%B5%D0%BD%D1%8C-f764a5b7-05fc-4494-9486-60d494efbf33',
            },
        ],
        functionParameter: {
            startDate: { name: 'начальная дата', detail: 'Начальная дата.' },
            days: { name: 'количество дней', detail: 'Количество дней до или после начальной даты, не являющихся выходными или праздниками. Положительное значение аргумента "количество_дней" обозначает дату в будущем, отрицательное — дату в прошлом.' },
            holidays: { name: 'праздники', detail: 'Список из одной или нескольких дат, которые требуется исключить из рабочего календаря, например государственные праздники. Список может представлять собой диапазон ячеек, содержащих даты, или константу массива, содержащую числа, которые представляют даты.' },
        },
    },
    WORKDAY_INTL: {
        description: 'Возвращает серийный номер даты до или после указанного количества рабочих дней с пользовательскими параметрами выходных',
        abstract: 'Возвращает серийный номер даты до или после указанного количества рабочих дней с пользовательскими параметрами выходных',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D1%80%D0%B0%D0%B1%D0%B4%D0%B5%D0%BD%D1%8C-%D0%BC%D0%B5%D0%B6%D0%B4-a378391c-9ba7-4678-8a39-39611a9bf81d',
            },
        ],
        functionParameter: {
            startDate: { name: 'начальная дата', detail: 'Начальная дата.' },
            days: { name: 'количество дней', detail: 'Количество дней до или после начальной даты, не являющихся выходными или праздниками. Положительное значение аргумента "количество_дней" обозначает дату в будущем, отрицательное — дату в прошлом.' },
            weekend: { name: 'выходные', detail: 'Указывает, какие дни недели являются выходными и не включаются в число рабочих дней между начальной и конечной датой.' },
            holidays: { name: 'праздники', detail: 'Список из одной или нескольких дат, которые требуется исключить из рабочего календаря, например государственные праздники. Список может представлять собой диапазон ячеек, содержащих даты, или константу массива, содержащую числа, которые представляют даты.' },
        },
    },
    YEAR: {
        description: 'Возвращает год, соответствующий заданной дате. Год определяется как целое число в диапазоне от 1900 до 9999',
        abstract: 'Возвращает год, соответствующий заданной дате',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B3%D0%BE%D0%B4-c64f017a-1354-490d-981f-578e8ec8d3b9',
            },
        ],
        functionParameter: {
            serialNumber: { name: 'дата в числовом формате', detail: 'Дата, год которой необходимо найти. Даты должны вводиться с помощью функции ДАТА или как результат вычисления других формул и функций. Например, для указания даты 23 мая 2008 г. воспользуйтесь выражением ДАТА(2008,5,23). Если ввести даты как текст, это может привести к возникновению проблем.' },
        },
    },
    YEARFRAC: {
        description: 'Returns the year fraction representing the number of whole days between start_date and end_date',
        abstract: 'Returns the year fraction representing the number of whole days between start_date and end_date',
        links: [
            {
                title: 'Инструкция',
                url: 'https://support.microsoft.com/ru-ru/office/%D1%84%D1%83%D0%BD%D0%BA%D1%86%D0%B8%D1%8F-%D0%B4%D0%BE%D0%BB%D1%8F%D0%B3%D0%BE%D0%B4%D0%B0-3844141e-c76d-4143-82b6-208454ddc6a8',
            },
        ],
        functionParameter: {
            startDate: { name: 'начальная дата', detail: 'Начальная дата.' },
            endDate: { name: 'конечная дата', detail: 'Конечная дата.' },
            basis: { name: 'базис', detail: 'Используемый способ вычисления дня' },
        },
    },
};

export default locale;
